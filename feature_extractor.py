import requests
import re
import tldextract
import validators
from bs4 import BeautifulSoup
from urllib.parse import urlparse, urljoin
import pandas as pd
import numpy as np
from typing import Dict, Any
import time
import warnings
warnings.filterwarnings('ignore')

class URLFeatureExtractor:
    """Extract 30 features from raw URLs for phishing detection"""

    def __init__(self, timeout=10):
        self.timeout = timeout
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

    def extract_features(self, url: str) -> Dict[str, Any]:
        """Extract all 30 features from a URL"""
        features = {}

        try:
            # Validate URL
            if not validators.url(url):
                url = f"http://{url}" if not url.startswith(('http://', 'https://')) else url

            # Basic URL parsing
            parsed_url = urlparse(url)
            domain = parsed_url.netloc.lower()

            # Extract TLD information
            tld_info = tldextract.extract(url)

            # Get webpage content
            html_content, response_info = self._get_webpage_content(url)
            soup = BeautifulSoup(html_content, 'html.parser') if html_content else None

            # Extract features
            features.update(self._extract_url_features(url, parsed_url, tld_info))
            features.update(self._extract_content_features(html_content, soup, url))
            features.update(self._extract_response_features(response_info))

        except Exception as e:
            print(f"Error extracting features: {e}")
            features = self._get_default_features()

        return features

    def _get_webpage_content(self, url: str):
        """Get webpage content and response information"""
        try:
            response = self.session.get(url, timeout=self.timeout, allow_redirects=True)
            return response.text, {
                'status_code': response.status_code,
                'headers': dict(response.headers),
                'is_https': url.startswith('https://'),
                'response_time': response.elapsed.total_seconds()
            }
        except Exception as e:
            print(f"Error fetching webpage: {e}")
            return "", {'status_code': 0, 'headers': {}, 'is_https': False, 'response_time': 0}

    def _extract_url_features(self, url: str, parsed_url, tld_info) -> Dict[str, Any]:
        """Extract URL-based features"""
        features = {}

        # URLLength
        features['URLLength'] = len(url)

        # TLD
        features['TLD'] = tld_info.suffix if tld_info.suffix else 'unknown'

        # URLSimilarityIndex (simplified implementation)
        features['URLSimilarityIndex'] = self._calculate_url_similarity(url)

        # CharContinuationRate
        features['CharContinuationRate'] = self._calculate_char_continuation_rate(url)

        # TLDLegitimateProb (simplified scoring)
        features['TLDLegitimateProb'] = self._calculate_tld_legitimacy(tld_info.suffix)

        # NoOfLettersInURL
        features['NoOfLettersInURL'] = sum(1 for c in url if c.isalpha())

        # LetterRatioInURL
        features['LetterRatioInURL'] = features['NoOfLettersInURL'] / len(url) if len(url) > 0 else 0

        # NoOfDegitsInURL
        features['NoOfDegitsInURL'] = sum(1 for c in url if c.isdigit())

        # NoOfOtherSpecialCharsInURL
        special_chars = sum(1 for c in url if not c.isalnum() and c not in [':', '/', '.', '-', '_', '?', '=', '&'])
        features['NoOfOtherSpecialCharsInURL'] = special_chars

        # IsHTTPS
        features['IsHTTPS'] = 1 if url.startswith('https://') else 0

        return features

    def _extract_content_features(self, html_content: str, soup, url: str) -> Dict[str, Any]:
        """Extract content-based features from HTML"""
        features = {}

        if not html_content or not soup:
            return self._get_default_content_features()

        # LineOfCode
        features['LineOfCode'] = len(html_content.split('\n'))

        # LargestLineLength
        lines = html_content.split('\n')
        features['LargestLineLength'] = max(len(line) for line in lines) if lines else 0

        # HasTitle
        title_tag = soup.find('title')
        features['HasTitle'] = 1 if title_tag and title_tag.get_text().strip() else 0

        # Title
        features['Title'] = title_tag.get_text().strip() if title_tag else ''

        # DomainTitleMatchScore
        features['DomainTitleMatchScore'] = self._calculate_domain_title_match(url, features['Title'])

        # URLTitleMatchScore
        features['URLTitleMatchScore'] = self._calculate_url_title_match(url, features['Title'])

        # HasFavicon
        favicon = soup.find('link', rel=lambda x: x and 'icon' in x.lower()) if soup else None
        features['HasFavicon'] = 1 if favicon else 0

        # IsResponsive
        viewport = soup.find('meta', attrs={'name': 'viewport'}) if soup else None
        features['IsResponsive'] = 1 if viewport else 0

        # HasDescription
        description = soup.find('meta', attrs={'name': 'description'}) if soup else None
        features['HasDescription'] = 1 if description and description.get('content', '').strip() else 0

        # NoOfiFrame
        iframes = soup.find_all('iframe') if soup else []
        features['NoOfiFrame'] = len(iframes)

        # HasSocialNet
        features['HasSocialNet'] = self._check_social_networks(soup)

        # HasSubmitButton
        submit_buttons = soup.find_all(['input', 'button'], type='submit') if soup else []
        features['HasSubmitButton'] = 1 if submit_buttons else 0

        # HasHiddenFields
        hidden_fields = soup.find_all('input', type='hidden') if soup else []
        features['HasHiddenFields'] = 1 if hidden_fields else 0

        # HasCopyrightInfo
        features['HasCopyrightInfo'] = self._check_copyright_info(html_content)

        # NoOfImage
        images = soup.find_all('img') if soup else []
        features['NoOfImage'] = len(images)

        # NoOfCSS
        css_links = soup.find_all('link', rel='stylesheet') if soup else []
        style_tags = soup.find_all('style') if soup else []
        features['NoOfCSS'] = len(css_links) + len(style_tags)

        # NoOfJS
        js_scripts = soup.find_all('script') if soup else []
        features['NoOfJS'] = len(js_scripts)

        # NoOfSelfRef, NoOfEmptyRef, NoOfExternalRef
        ref_counts = self._count_references(soup, url)
        features.update(ref_counts)

        return features

    def _extract_response_features(self, response_info: Dict) -> Dict[str, Any]:
        """Extract features from HTTP response"""
        return {}  # Already handled in other methods

    def _calculate_url_similarity(self, url: str) -> float:
        """Calculate URL similarity index (simplified)"""
        # Count repeated characters and patterns
        char_counts = {}
        for char in url.lower():
            char_counts[char] = char_counts.get(char, 0) + 1

        # Calculate similarity based on character repetition
        total_chars = len(url)
        unique_chars = len(char_counts)
        return 1 - (unique_chars / total_chars) if total_chars > 0 else 0

    def _calculate_char_continuation_rate(self, url: str) -> float:
        """Calculate character continuation rate"""
        if len(url) <= 1:
            return 0

        continuations = 0
        for i in range(1, len(url)):
            if url[i] == url[i-1]:
                continuations += 1

        return continuations / (len(url) - 1)

    def _calculate_tld_legitimacy(self, tld: str) -> float:
        """Calculate TLD legitimacy probability"""
        legitimate_tlds = {
            'com': 0.9, 'org': 0.8, 'net': 0.7, 'edu': 0.95, 'gov': 0.98,
            'mil': 0.95, 'int': 0.9, 'co': 0.6, 'info': 0.5, 'biz': 0.4
        }
        return legitimate_tlds.get(tld, 0.3)

    def _calculate_domain_title_match(self, url: str, title: str) -> float:
        """Calculate domain-title match score"""
        if not title:
            return 0

        domain = urlparse(url).netloc.lower()
        title_lower = title.lower()

        # Extract domain name without TLD
        domain_parts = domain.split('.')
        domain_name = domain_parts[0] if domain_parts else domain

        # Simple matching algorithm
        if domain_name in title_lower:
            return 1.0

        # Check for partial matches
        common_chars = set(domain_name) & set(title_lower.replace(' ', ''))
        if len(common_chars) > 0:
            return len(common_chars) / len(set(domain_name))

        return 0

    def _calculate_url_title_match(self, url: str, title: str) -> float:
        """Calculate URL-title match score"""
        if not title:
            return 0

        url_lower = url.lower()
        title_lower = title.lower()

        # Remove common words
        title_words = [word for word in title_lower.split() if len(word) > 3]

        matches = 0
        for word in title_words:
            if word in url_lower:
                matches += 1

        return matches / len(title_words) if title_words else 0

    def _check_social_networks(self, soup) -> int:
        """Check for social network links"""
        if not soup:
            return 0

        social_domains = ['facebook.com', 'twitter.com', 'instagram.com', 'linkedin.com',
                         'youtube.com', 'tiktok.com', 'pinterest.com']

        links = soup.find_all('a', href=True)
        for link in links:
            href = link['href'].lower()
            if any(domain in href for domain in social_domains):
                return 1
        return 0

    def _check_copyright_info(self, html_content: str) -> int:
        """Check for copyright information"""
        if not html_content:
            return 0

        copyright_patterns = [r'©', r'copyright', r'\(c\)', r'&copy;']
        content_lower = html_content.lower()

        for pattern in copyright_patterns:
            if re.search(pattern, content_lower):
                return 1
        return 0

    def _count_references(self, soup, url: str) -> Dict[str, int]:
        """Count different types of references"""
        if not soup:
            return {'NoOfSelfRef': 0, 'NoOfEmptyRef': 0, 'NoOfExternalRef': 0}

        domain = urlparse(url).netloc.lower()
        self_ref = 0
        empty_ref = 0
        external_ref = 0

        # Check all links
        links = soup.find_all(['a', 'link', 'script', 'img'], href=True) + \
                soup.find_all(['a', 'link', 'script', 'img'], src=True)

        for element in links:
            ref_url = element.get('href') or element.get('src', '')

            if not ref_url or ref_url in ['#', '', 'javascript:void(0)']:
                empty_ref += 1
            elif ref_url.startswith(('http://', 'https://')):
                ref_domain = urlparse(ref_url).netloc.lower()
                if ref_domain == domain:
                    self_ref += 1
                else:
                    external_ref += 1
            else:
                self_ref += 1  # Relative URLs are self-references

        return {
            'NoOfSelfRef': self_ref,
            'NoOfEmptyRef': empty_ref,
            'NoOfExternalRef': external_ref
        }

    def _get_default_features(self) -> Dict[str, Any]:
        """Return default feature values when extraction fails"""
        return {
            'URLLength': 0, 'TLD': 'unknown', 'URLSimilarityIndex': 0,
            'CharContinuationRate': 0, 'TLDLegitimateProb': 0, 'NoOfLettersInURL': 0,
            'LetterRatioInURL': 0, 'NoOfDegitsInURL': 0, 'NoOfOtherSpecialCharsInURL': 0,
            'IsHTTPS': 0, 'LineOfCode': 0, 'LargestLineLength': 0, 'HasTitle': 0,
            'Title': '', 'DomainTitleMatchScore': 0, 'URLTitleMatchScore': 0,
            'HasFavicon': 0, 'IsResponsive': 0, 'HasDescription': 0, 'NoOfiFrame': 0,
            'HasSocialNet': 0, 'HasSubmitButton': 0, 'HasHiddenFields': 0,
            'HasCopyrightInfo': 0, 'NoOfImage': 0, 'NoOfCSS': 0, 'NoOfJS': 0,
            'NoOfSelfRef': 0, 'NoOfEmptyRef': 0, 'NoOfExternalRef': 0
        }

    def _get_default_content_features(self) -> Dict[str, Any]:
        """Return default content features when HTML parsing fails"""
        return {
            'LineOfCode': 0, 'LargestLineLength': 0, 'HasTitle': 0, 'Title': '',
            'DomainTitleMatchScore': 0, 'URLTitleMatchScore': 0, 'HasFavicon': 0,
            'IsResponsive': 0, 'HasDescription': 0, 'NoOfiFrame': 0, 'HasSocialNet': 0,
            'HasSubmitButton': 0, 'HasHiddenFields': 0, 'HasCopyrightInfo': 0,
            'NoOfImage': 0, 'NoOfCSS': 0, 'NoOfJS': 0, 'NoOfSelfRef': 0,
            'NoOfEmptyRef': 0, 'NoOfExternalRef': 0
        }