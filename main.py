from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
import joblib
import pandas as pd
import numpy as np
from feature_extractor import URLFeatureExtractor
import os
import traceback
from typing import Dict, Any
import warnings
warnings.filterwarnings('ignore')

# Initialize FastAPI app
app = FastAPI(title="Phishing URL Detection System", version="1.0.0")

# Mount static files and templates
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# Global variables for models
preprocessor = None
stacking_model = None
feature_extractor = None

# Load models on startup
@app.on_event("startup")
async def load_models():
    global preprocessor, stacking_model, feature_extractor

    try:
        # Load the preprocessor and model
        if os.path.exists("features_preprocessor.joblib"):
            preprocessor = joblib.load("features_preprocessor.joblib")
            print("✅ Preprocessor loaded successfully")
        else:
            print("❌ Preprocessor file not found")

        if os.path.exists("final_stacking_model.joblib"):
            stacking_model = joblib.load("final_stacking_model.joblib")
            print("✅ Stacking model loaded successfully")
        else:
            print("❌ Stacking model file not found")

        # Initialize feature extractor
        feature_extractor = URLFeatureExtractor(timeout=10)
        print("✅ Feature extractor initialized")

    except Exception as e:
        print(f"❌ Error loading models: {e}")
        traceback.print_exc()

# Request/Response models
class URLRequest(BaseModel):
    url: str

class PredictionResponse(BaseModel):
    url: str
    prediction: str
    probability: float
    confidence: str
    features: Dict[str, Any]
    processing_time: float

# Main page
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

# Health check endpoint
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "preprocessor_loaded": preprocessor is not None,
        "model_loaded": stacking_model is not None,
        "feature_extractor_ready": feature_extractor is not None
    }

# Prediction endpoint
@app.post("/predict", response_model=PredictionResponse)
async def predict_url(url_request: URLRequest):
    global preprocessor, stacking_model, feature_extractor

    if not all([preprocessor, stacking_model, feature_extractor]):
        raise HTTPException(
            status_code=500,
            detail="Models not loaded properly. Check server logs."
        )

    try:
        import time
        start_time = time.time()

        url = url_request.url.strip()

        # Validate URL format
        if not url:
            raise HTTPException(status_code=400, detail="URL cannot be empty")

        # Extract features
        print(f"🔍 Extracting features for: {url}")
        features = feature_extractor.extract_features(url)

        # Convert to DataFrame
        feature_df = pd.DataFrame([features])

        # Ensure all required columns are present
        required_features = [
            'URLLength', 'TLD', 'URLSimilarityIndex', 'CharContinuationRate',
            'TLDLegitimateProb', 'NoOfLettersInURL', 'LetterRatioInURL',
            'NoOfDegitsInURL', 'NoOfOtherSpecialCharsInURL', 'IsHTTPS',
            'LineOfCode', 'LargestLineLength', 'HasTitle', 'Title',
            'DomainTitleMatchScore', 'URLTitleMatchScore', 'HasFavicon',
            'IsResponsive', 'HasDescription', 'NoOfiFrame', 'HasSocialNet',
            'HasSubmitButton', 'HasHiddenFields', 'HasCopyrightInfo',
            'NoOfImage', 'NoOfCSS', 'NoOfJS', 'NoOfSelfRef',
            'NoOfEmptyRef', 'NoOfExternalRef'
        ]

        # Add missing features with default values
        for feature in required_features:
            if feature not in feature_df.columns:
                if feature == 'TLD':
                    feature_df[feature] = 'unknown'
                elif feature == 'Title':
                    feature_df[feature] = ''
                else:
                    feature_df[feature] = 0

        # Reorder columns to match training data
        feature_df = feature_df[required_features]

        print("🔧 Preprocessing features...")
        # Preprocess features
        processed_features = preprocessor.transform(feature_df)

        print("🤖 Making prediction...")
        # Make prediction
        prediction_proba = stacking_model.predict_proba(processed_features)[0]
        prediction = stacking_model.predict(processed_features)[0]

        # Calculate confidence (probability of predicted class)
        phishing_prob = prediction_proba[0]  # Probability of phishing (class 0)
        legitimate_prob = prediction_proba[1]  # Probability of legitimate (class 1)

        # Determine prediction and confidence
        if prediction == 0:
            result = "Phishing"
            confidence_score = phishing_prob
        else:
            result = "Legitimate"
            confidence_score = legitimate_prob

        # Determine confidence level
        if confidence_score >= 0.8:
            confidence_level = "High"
        elif confidence_score >= 0.6:
            confidence_level = "Medium"
        else:
            confidence_level = "Low"

        processing_time = time.time() - start_time

        print(f"✅ Prediction complete: {result} ({confidence_score:.3f})")

        return PredictionResponse(
            url=url,
            prediction=result,
            probability=float(phishing_prob),  # Always return phishing probability
            confidence=f"{confidence_level} ({confidence_score:.3f})",
            features=features,
            processing_time=round(processing_time, 3)
        )

    except Exception as e:
        print(f"❌ Error during prediction: {e}")
        traceback.print_exc()
        raise HTTPException(
            status_code=500,
            detail=f"Error processing URL: {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
