#!/usr/bin/env python3
"""
Startup script for the Phishing Detection System
"""

import os
import sys
import subprocess
import time

def check_requirements():
    """Check if all required files exist"""
    print("🔍 Checking system requirements...")
    
    required_files = [
        "main.py",
        "feature_extractor.py", 
        "preprocessor.py",
        "features_preprocessor.joblib",
        "final_stacking_model.joblib",
        "templates/index.html",
        "static/style.css",
        "static/script.js"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
        else:
            print(f"✅ {file}")
    
    if missing_files:
        print(f"\n❌ Missing files:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ All required files found!")
    return True

def install_dependencies():
    """Install required dependencies"""
    print("\n📦 Installing dependencies...")
    
    if not os.path.exists("requirements.txt"):
        print("❌ requirements.txt not found!")
        return False
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True)
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def start_server():
    """Start the FastAPI server"""
    print("\n🚀 Starting Phishing Detection System...")
    print("📍 Server will be available at: http://localhost:8000")
    print("🛑 Press Ctrl+C to stop the server\n")
    
    try:
        subprocess.run([
            sys.executable, "-m", "uvicorn", "main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ])
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")

def main():
    """Main startup function"""
    print("=" * 60)
    print("🛡️  PHISHING URL DETECTION SYSTEM")
    print("=" * 60)
    
    # Check requirements
    if not check_requirements():
        print("\n❌ System check failed. Please ensure all files are present.")
        return
    
    # Ask user about dependency installation
    install_deps = input("\n📦 Install/update dependencies? (y/n): ").lower().strip()
    if install_deps in ['y', 'yes']:
        if not install_dependencies():
            print("❌ Dependency installation failed.")
            return
    
    # Start server
    print("\n" + "=" * 60)
    print("🚀 STARTING SERVER")
    print("=" * 60)
    
    start_server()

if __name__ == "__main__":
    main()
