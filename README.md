# 🛡️ Phishing URL Detection System

A complete machine learning-powered system for detecting phishing URLs using real-time feature extraction and ensemble modeling.

## 🌟 Features

- **Real-time URL Analysis**: Extract 30 features from any URL with internet connectivity
- **Advanced ML Pipeline**: Uses preprocessing and stacking ensemble models
- **Web Interface**: Clean, responsive web UI for easy URL testing
- **REST API**: FastAPI backend for programmatic access
- **Comprehensive Feature Extraction**: Analyzes URL structure, HTML content, and web properties

## 📋 Requirements

- Python 3.8+
- Internet connection (for URL analysis)
- Pre-trained models: `features_preprocessor.joblib` and `final_stacking_model.joblib`

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Start the System
```bash
python run_server.py
```

### 3. Access the Web Interface
Open your browser and go to: `http://localhost:8000`

## 📁 Project Structure

```
├── main.py                          # FastAPI application
├── feature_extractor.py             # URL feature extraction
├── preprocessor.py                  # Data preprocessing pipeline
├── utils.py                         # Utility functions
├── run_server.py                    # Server startup script
├── test_system.py                   # System testing script
├── requirements.txt                 # Python dependencies
├── features_preprocessor.joblib     # Trained preprocessor
├── final_stacking_model.joblib      # Trained ML model
├── templates/
│   └── index.html                   # Web interface
└── static/
    ├── style.css                    # Styling
    └── script.js                    # Frontend logic
```

## 🔧 API Usage

### Health Check
```bash
curl http://localhost:8000/health
```

### Predict URL
```bash
curl -X POST "http://localhost:8000/predict" \
     -H "Content-Type: application/json" \
     -d '{"url": "https://www.google.com/"}'
```

### Response Format
```json
{
  "url": "https://www.google.com/",
  "prediction": "Legitimate",
  "probability": 0.123,
  "confidence": "High (0.877)",
  "features": {
    "URLLength": 23,
    "TLD": "com",
    "IsHTTPS": 1,
    ...
  },
  "processing_time": 2.456
}
```

## 📊 Extracted Features (30 Total)

### URL Structure Features
- `URLLength`: Total URL length
- `TLD`: Top-level domain
- `URLSimilarityIndex`: Character similarity index
- `CharContinuationRate`: Consecutive character rate
- `TLDLegitimateProb`: TLD legitimacy probability
- `NoOfLettersInURL`: Letter count
- `LetterRatioInURL`: Letter to total character ratio
- `NoOfDegitsInURL`: Digit count
- `NoOfOtherSpecialCharsInURL`: Special character count
- `IsHTTPS`: HTTPS protocol usage

### Content Features
- `LineOfCode`: HTML line count
- `LargestLineLength`: Longest HTML line
- `HasTitle`: Title tag presence
- `Title`: Page title content
- `DomainTitleMatchScore`: Domain-title similarity
- `URLTitleMatchScore`: URL-title similarity
- `HasFavicon`: Favicon presence
- `IsResponsive`: Mobile responsiveness
- `HasDescription`: Meta description presence
- `NoOfiFrame`: Iframe count
- `HasSocialNet`: Social media links
- `HasSubmitButton`: Submit button presence
- `HasHiddenFields`: Hidden form fields
- `HasCopyrightInfo`: Copyright information
- `NoOfImage`: Image count
- `NoOfCSS`: CSS file count
- `NoOfJS`: JavaScript file count
- `NoOfSelfRef`: Self-reference count
- `NoOfEmptyRef`: Empty reference count
- `NoOfExternalRef`: External reference count

## 🧪 Testing

Run the test suite:
```bash
python test_system.py
```

## 🔄 Preprocessing Pipeline

The system uses a sophisticated preprocessing pipeline:

1. **Target Encoding**: Categorical features (TLD, Title)
2. **Variance Threshold**: Remove low-variance features
3. **Feature Selection**: Select top K features using mutual information
4. **Standardization**: Scale features using StandardScaler

## 🎯 Model Interpretation

- **Probability**: Ranges from 0 to 1
  - Closer to 0: More likely legitimate
  - Closer to 1: More likely phishing
- **Confidence Levels**:
  - High: ≥ 80% confidence
  - Medium: 60-79% confidence  
  - Low: < 60% confidence

## ⚠️ Important Notes

1. **Internet Required**: The system needs internet access to analyze URLs
2. **Processing Time**: Analysis may take 5-15 seconds depending on website response
3. **Model Files**: Ensure `features_preprocessor.joblib` and `final_stacking_model.joblib` are present
4. **Rate Limiting**: Be mindful of making too many requests to avoid being blocked

## 🛠️ Troubleshooting

### Common Issues

1. **Models not loading**: Ensure `.joblib` files are in the root directory
2. **Feature extraction fails**: Check internet connection and URL validity
3. **Server won't start**: Verify all dependencies are installed
4. **Slow processing**: Some websites may take longer to analyze

### Debug Mode
Set environment variable for detailed logging:
```bash
export DEBUG=1
python main.py
```

## 📈 Performance

- **Feature Extraction**: 2-10 seconds per URL
- **Prediction**: < 100ms after feature extraction
- **Memory Usage**: ~200MB with models loaded
- **Accuracy**: Depends on training data quality

## 🔒 Security Considerations

- The system makes HTTP requests to analyze URLs
- No user data is stored permanently
- Use in controlled environments for sensitive URLs
- Consider proxy/VPN for additional privacy

## 📝 License

This project is for educational and research purposes.
