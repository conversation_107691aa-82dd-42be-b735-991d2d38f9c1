<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phishing URL Detection System</title>
    <link rel="stylesheet" href="/static/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-shield-alt"></i> Phishing URL Detection System</h1>
            <p>Enter a URL to check if it's legitimate or phishing</p>
        </header>

        <main>
            <div class="input-section">
                <form id="urlForm">
                    <div class="input-group">
                        <input 
                            type="text" 
                            id="urlInput" 
                            placeholder="Enter URL (e.g., https://www.google.com/)" 
                            required
                        >
                        <button type="submit" id="analyzeBtn">
                            <i class="fas fa-search"></i> Analyze URL
                        </button>
                    </div>
                </form>
            </div>

            <div id="loadingSection" class="loading-section" style="display: none;">
                <div class="spinner"></div>
                <p>Analyzing URL... This may take a few seconds</p>
            </div>

            <div id="resultSection" class="result-section" style="display: none;">
                <div class="result-card">
                    <div class="result-header">
                        <h2 id="resultTitle"></h2>
                        <div id="resultIcon"></div>
                    </div>
                    
                    <div class="result-details">
                        <div class="detail-item">
                            <strong>URL:</strong>
                            <span id="analyzedUrl"></span>
                        </div>
                        
                        <div class="detail-item">
                            <strong>Classification:</strong>
                            <span id="classification"></span>
                        </div>
                        
                        <div class="detail-item">
                            <strong>Phishing Probability:</strong>
                            <span id="probability"></span>
                        </div>
                        
                        <div class="detail-item">
                            <strong>Confidence:</strong>
                            <span id="confidence"></span>
                        </div>
                        
                        <div class="detail-item">
                            <strong>Processing Time:</strong>
                            <span id="processingTime"></span>
                        </div>
                    </div>

                    <div class="probability-bar">
                        <div class="bar-container">
                            <div class="bar-fill" id="probabilityBar"></div>
                        </div>
                        <div class="bar-labels">
                            <span>Legitimate</span>
                            <span>Phishing</span>
                        </div>
                    </div>
                </div>

                <div class="features-section">
                    <h3><i class="fas fa-cogs"></i> Extracted Features</h3>
                    <div id="featuresGrid" class="features-grid"></div>
                </div>
            </div>

            <div id="errorSection" class="error-section" style="display: none;">
                <div class="error-card">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>Error</h3>
                    <p id="errorMessage"></p>
                </div>
            </div>
        </main>

        <footer>
            <p>Powered by Machine Learning • Built with FastAPI</p>
        </footer>
    </div>

    <script src="/static/script.js"></script>
</body>
</html>
