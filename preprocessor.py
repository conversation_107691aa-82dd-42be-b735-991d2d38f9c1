import pandas as pd
import numpy as np
from sklearn.base import BaseEstimator, TransformerMixin
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import VarianceThreshold, SelectKBest, mutual_info_classif
from category_encoders import TargetEncoder
import warnings
warnings.filterwarnings('ignore')

# Random state for reproducibility
RANDOM_STATE = 42

class PhishingPreprocessor(BaseEstimator, TransformerMixin):
    """Custom preprocessor for phishing URL data"""
    
    def __init__(self, categorical_cols=None, n_features=30):
        self.categorical_cols = categorical_cols or []
        self.n_features = n_features
        self.target_encoder = TargetEncoder(random_state=RANDOM_STATE)
        self.variance_selector = VarianceThreshold(threshold=0.01)
        self.feature_selector = SelectKBest(mutual_info_classif, k=n_features)
        self.scaler = StandardScaler()
        self.selected_features = None
        
    def fit(self, X, y=None):
        X_work = X.copy()
        
        # Target encoding for categorical features
        if self.categorical_cols:
            cat_data = X_work[self.categorical_cols]
            self.target_encoder.fit(cat_data, y)
            X_work[self.categorical_cols] = self.target_encoder.transform(cat_data)
        
        # Variance threshold
        X_work = self.variance_selector.fit_transform(X_work)
        
        # Feature selection
        X_work = self.feature_selector.fit_transform(X_work, y)
        
        # Store selected feature names
        if hasattr(self.variance_selector, 'get_feature_names_out'):
            var_features = self.variance_selector.get_feature_names_out(X.columns)
        else:
            var_features = X.columns[self.variance_selector.get_support()]
        
        if hasattr(self.feature_selector, 'get_feature_names_out'):
            self.selected_features = self.feature_selector.get_feature_names_out(var_features)
        else:
            selected_indices = self.feature_selector.get_support()
            self.selected_features = var_features[selected_indices]
        
        # Scaling
        self.scaler.fit(X_work)
        
        return self
    
    def transform(self, X):
        X_work = X.copy()
        
        # Target encoding
        if self.categorical_cols:
            cat_data = X_work[self.categorical_cols]
            X_work[self.categorical_cols] = self.target_encoder.transform(cat_data)
        
        # Variance threshold
        X_work = self.variance_selector.transform(X_work)
        
        # Feature selection
        X_work = self.feature_selector.transform(X_work)
        
        # Scaling
        X_work = self.scaler.transform(X_work)
        
        return X_work
