* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.input-section {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 30px;
}

.input-group {
    display: flex;
    gap: 15px;
    align-items: center;
}

#urlInput {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

#urlInput:focus {
    outline: none;
    border-color: #667eea;
}

#analyzeBtn {
    padding: 15px 25px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    cursor: pointer;
    transition: transform 0.2s ease;
}

#analyzeBtn:hover {
    transform: translateY(-2px);
}

.loading-section {
    text-align: center;
    padding: 40px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 30px;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.result-section {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    overflow: hidden;
    margin-bottom: 30px;
}

.result-card {
    padding: 30px;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f0f0f0;
}

.result-header h2 {
    font-size: 1.8rem;
}

.result-header .legitimate {
    color: #28a745;
}

.result-header .phishing {
    color: #dc3545;
}

#resultIcon {
    font-size: 3rem;
}

.result-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.detail-item {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.detail-item strong {
    display: block;
    margin-bottom: 5px;
    color: #495057;
}

.probability-bar {
    margin-top: 25px;
}

.bar-container {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.bar-fill {
    height: 100%;
    transition: width 0.5s ease;
    border-radius: 10px;
}

.bar-fill.low {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.bar-fill.medium {
    background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.bar-fill.high {
    background: linear-gradient(90deg, #dc3545, #e74c3c);
}

.bar-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: #6c757d;
}

.features-section {
    background: #f8f9fa;
    padding: 30px;
    border-top: 1px solid #e9ecef;
}

.features-section h3 {
    margin-bottom: 20px;
    color: #495057;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.feature-item {
    background: white;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.feature-name {
    font-weight: bold;
    color: #495057;
    margin-bottom: 5px;
}

.feature-value {
    color: #6c757d;
    font-size: 0.9rem;
}

.error-section {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 30px;
}

.error-card {
    padding: 30px;
    text-align: center;
    color: #dc3545;
}

.error-card i {
    font-size: 3rem;
    margin-bottom: 15px;
}

footer {
    text-align: center;
    color: white;
    opacity: 0.8;
    margin-top: 40px;
}

@media (max-width: 768px) {
    .input-group {
        flex-direction: column;
    }
    
    .result-details {
        grid-template-columns: 1fr;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    header h1 {
        font-size: 2rem;
    }
}
