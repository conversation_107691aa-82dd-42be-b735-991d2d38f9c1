import re
import validators
from urllib.parse import urlparse
from typing import Optional

def validate_url(url: str) -> tuple[bool, Optional[str]]:
    """
    Validate URL format and return normalized URL

    Args:
        url: Raw URL string

    Returns:
        Tuple of (is_valid, normalized_url)
    """
    if not url or not url.strip():
        return False, None

    url = url.strip()

    # Add protocol if missing
    if not url.startswith(('http://', 'https://')):
        url = f"http://{url}"

    # Validate URL format
    if validators.url(url):
        return True, url
    else:
        return False, None

def extract_domain(url: str) -> Optional[str]:
    """Extract domain from URL"""
    try:
        parsed = urlparse(url)
        return parsed.netloc.lower()
    except:
        return None

def is_suspicious_url(url: str) -> dict:
    """
    Check for suspicious URL patterns

    Returns:
        Dictionary with suspicion indicators
    """
    suspicion_indicators = {
        'has_ip_address': bool(re.search(r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b', url)),
        'excessive_subdomains': len(url.split('.')) > 5,
        'suspicious_tld': any(tld in url.lower() for tld in ['.tk', '.ml', '.ga', '.cf']),
        'url_shortener': any(shortener in url.lower() for shortener in ['bit.ly', 'tinyurl', 't.co', 'goo.gl']),
        'excessive_length': len(url) > 200,
        'suspicious_chars': bool(re.search(r'[^\w\-\.\/\:\?\=\&\%]', url)),
        'homograph_attack': _check_homograph_attack(url)
    }

    return suspicion_indicators

def _check_homograph_attack(url: str) -> bool:
    """Check for potential homograph attacks"""
    # Simplified check for mixed scripts or suspicious Unicode characters
    suspicious_chars = [
        '\u0430',  # Cyrillic 'a'
        '\u043e',  # Cyrillic 'o'
        '\u0440',  # Cyrillic 'p'
        '\u0435',  # Cyrillic 'e'
    ]

    return any(char in url for char in suspicious_chars)

def format_feature_value(value) -> str:
    """Format feature values for display"""
    if isinstance(value, float):
        return f"{value:.3f}"
    elif isinstance(value, bool):
        return "Yes" if value else "No"
    elif isinstance(value, str) and len(value) > 50:
        return value[:50] + "..."
    else:
        return str(value)

def get_risk_level(probability: float) -> tuple[str, str]:
    """
    Get risk level and color based on phishing probability

    Args:
        probability: Phishing probability (0-1)

    Returns:
        Tuple of (risk_level, color_class)
    """
    if probability >= 0.8:
        return "High Risk", "danger"
    elif probability >= 0.6:
        return "Medium Risk", "warning"
    elif probability >= 0.4:
        return "Low Risk", "info"
    else:
        return "Very Low Risk", "success"
