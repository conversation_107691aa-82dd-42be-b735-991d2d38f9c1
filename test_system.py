#!/usr/bin/env python3
"""
Test script for the phishing detection system
"""

import requests
import json
import time
from feature_extractor import URLFeatureExtractor

def test_feature_extraction():
    """Test feature extraction functionality"""
    print("🧪 Testing Feature Extraction...")
    
    extractor = URLFeatureExtractor(timeout=5)
    test_urls = [
        "https://www.google.com/",
        "https://github.com/",
        "https://stackoverflow.com/"
    ]
    
    for url in test_urls:
        print(f"\n📊 Extracting features for: {url}")
        try:
            features = extractor.extract_features(url)
            print(f"✅ Extracted {len(features)} features")
            
            # Print some key features
            key_features = ['URLLength', 'TLD', 'IsHTTPS', 'HasTitle', 'NoOfImage']
            for feature in key_features:
                if feature in features:
                    print(f"   {feature}: {features[feature]}")
                    
        except Exception as e:
            print(f"❌ Error: {e}")

def test_api_endpoint():
    """Test the FastAPI endpoint"""
    print("\n🌐 Testing API Endpoint...")
    
    base_url = "http://localhost:8000"
    test_urls = [
        "https://www.google.com/",
        "https://github.com/",
        "invalid-url"
    ]
    
    # Test health check
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Health check passed: {health_data}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        return
    
    # Test prediction endpoint
    for url in test_urls:
        print(f"\n🔍 Testing prediction for: {url}")
        try:
            payload = {"url": url}
            response = requests.post(
                f"{base_url}/predict", 
                json=payload, 
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Prediction: {data['prediction']}")
                print(f"   Probability: {data['probability']:.3f}")
                print(f"   Confidence: {data['confidence']}")
                print(f"   Processing time: {data['processing_time']}s")
            else:
                error_data = response.json()
                print(f"❌ Error {response.status_code}: {error_data.get('detail', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ Request failed: {e}")

def check_model_files():
    """Check if required model files exist"""
    print("📁 Checking Model Files...")
    
    import os
    required_files = [
        "features_preprocessor.joblib",
        "final_stacking_model.joblib"
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} found")
        else:
            print(f"❌ {file} missing")

def main():
    """Run all tests"""
    print("🚀 Starting Phishing Detection System Tests\n")
    
    # Check model files
    check_model_files()
    
    # Test feature extraction
    test_feature_extraction()
    
    # Test API (only if server is running)
    print("\n" + "="*50)
    print("Note: Make sure the FastAPI server is running on localhost:8000")
    print("Run: python main.py")
    print("="*50)
    
    user_input = input("\nPress Enter to test API endpoints (or 'skip' to skip): ")
    if user_input.lower() != 'skip':
        test_api_endpoint()
    
    print("\n🎉 Tests completed!")

if __name__ == "__main__":
    main()
