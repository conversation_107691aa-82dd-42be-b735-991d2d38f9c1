document.addEventListener('DOMContentLoaded', function() {
    const urlForm = document.getElementById('urlForm');
    const urlInput = document.getElementById('urlInput');
    const analyzeBtn = document.getElementById('analyzeBtn');
    const loadingSection = document.getElementById('loadingSection');
    const resultSection = document.getElementById('resultSection');
    const errorSection = document.getElementById('errorSection');

    urlForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const url = urlInput.value.trim();
        if (!url) {
            showError('Please enter a URL');
            return;
        }

        // Hide previous results
        hideAllSections();
        showLoading();

        try {
            const response = await fetch('/predict', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ url: url })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.detail || 'An error occurred');
            }

            showResult(data);

        } catch (error) {
            console.error('Error:', error);
            showError(error.message || 'Failed to analyze URL');
        } finally {
            hideLoading();
        }
    });

    function hideAllSections() {
        loadingSection.style.display = 'none';
        resultSection.style.display = 'none';
        errorSection.style.display = 'none';
    }

    function showLoading() {
        loadingSection.style.display = 'block';
        analyzeBtn.disabled = true;
        analyzeBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Analyzing...';
    }

    function hideLoading() {
        loadingSection.style.display = 'none';
        analyzeBtn.disabled = false;
        analyzeBtn.innerHTML = '<i class="fas fa-search"></i> Analyze URL';
    }

    function showResult(data) {
        // Update result header
        const resultTitle = document.getElementById('resultTitle');
        const resultIcon = document.getElementById('resultIcon');
        
        if (data.prediction === 'Phishing') {
            resultTitle.textContent = 'Phishing Detected';
            resultTitle.className = 'phishing';
            resultIcon.innerHTML = '<i class="fas fa-exclamation-triangle" style="color: #dc3545;"></i>';
        } else {
            resultTitle.textContent = 'Legitimate Website';
            resultTitle.className = 'legitimate';
            resultIcon.innerHTML = '<i class="fas fa-check-circle" style="color: #28a745;"></i>';
        }

        // Update result details
        document.getElementById('analyzedUrl').textContent = data.url;
        document.getElementById('classification').textContent = data.prediction;
        document.getElementById('probability').textContent = `${(data.probability * 100).toFixed(1)}%`;
        document.getElementById('confidence').textContent = data.confidence;
        document.getElementById('processingTime').textContent = `${data.processing_time}s`;

        // Update probability bar
        const probabilityBar = document.getElementById('probabilityBar');
        const probabilityPercent = data.probability * 100;
        
        probabilityBar.style.width = `${probabilityPercent}%`;
        
        if (probabilityPercent < 30) {
            probabilityBar.className = 'bar-fill low';
        } else if (probabilityPercent < 70) {
            probabilityBar.className = 'bar-fill medium';
        } else {
            probabilityBar.className = 'bar-fill high';
        }

        // Update features
        displayFeatures(data.features);

        resultSection.style.display = 'block';
    }

    function displayFeatures(features) {
        const featuresGrid = document.getElementById('featuresGrid');
        featuresGrid.innerHTML = '';

        // Define feature descriptions
        const featureDescriptions = {
            'URLLength': 'Total length of the URL',
            'TLD': 'Top-level domain',
            'URLSimilarityIndex': 'URL character similarity index',
            'CharContinuationRate': 'Rate of consecutive identical characters',
            'TLDLegitimateProb': 'TLD legitimacy probability',
            'NoOfLettersInURL': 'Number of letters in URL',
            'LetterRatioInURL': 'Ratio of letters to total characters',
            'NoOfDegitsInURL': 'Number of digits in URL',
            'NoOfOtherSpecialCharsInURL': 'Number of special characters',
            'IsHTTPS': 'Uses HTTPS protocol',
            'LineOfCode': 'Number of lines in HTML',
            'LargestLineLength': 'Length of longest HTML line',
            'HasTitle': 'Has HTML title tag',
            'Title': 'Page title',
            'DomainTitleMatchScore': 'Domain-title match score',
            'URLTitleMatchScore': 'URL-title match score',
            'HasFavicon': 'Has favicon',
            'IsResponsive': 'Is mobile responsive',
            'HasDescription': 'Has meta description',
            'NoOfiFrame': 'Number of iframes',
            'HasSocialNet': 'Has social media links',
            'HasSubmitButton': 'Has submit buttons',
            'HasHiddenFields': 'Has hidden form fields',
            'HasCopyrightInfo': 'Has copyright information',
            'NoOfImage': 'Number of images',
            'NoOfCSS': 'Number of CSS files',
            'NoOfJS': 'Number of JavaScript files',
            'NoOfSelfRef': 'Number of self-references',
            'NoOfEmptyRef': 'Number of empty references',
            'NoOfExternalRef': 'Number of external references'
        };

        Object.entries(features).forEach(([key, value]) => {
            const featureItem = document.createElement('div');
            featureItem.className = 'feature-item';
            
            let displayValue = value;
            if (typeof value === 'number') {
                displayValue = value % 1 === 0 ? value : value.toFixed(3);
            } else if (typeof value === 'string' && value.length > 50) {
                displayValue = value.substring(0, 50) + '...';
            }
            
            featureItem.innerHTML = `
                <div class="feature-name">${key}</div>
                <div class="feature-value">${displayValue}</div>
                <div class="feature-description">${featureDescriptions[key] || 'Feature description'}</div>
            `;
            
            featuresGrid.appendChild(featureItem);
        });
    }

    function showError(message) {
        document.getElementById('errorMessage').textContent = message;
        errorSection.style.display = 'block';
    }

    // Add some example URLs for testing
    const exampleUrls = [
        'https://www.google.com/',
        'https://www.github.com/',
        'https://www.stackoverflow.com/'
    ];

    // Add click handler for input to show placeholder examples
    urlInput.addEventListener('focus', function() {
        if (!this.value) {
            this.placeholder = exampleUrls[Math.floor(Math.random() * exampleUrls.length)];
        }
    });
});
